import React from 'react';
import { Box, Container, Typography, Grid } from '@mui/material';
import AgentCard from './AgentCard';
import MarketplaceFilters from './MarketplaceFilters';
import MarketplaceSearch from './MarketplaceSearch';

interface Agent {
  id: string;
  name: string;
  description: string;
  price: number;
  rating: number;
  category: string;
  imageUrl: string;
}

const Marketplace: React.FC = () => {
  const [agents, setAgents] = React.useState<Agent[]>([]);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedFilters, setSelectedFilters] = React.useState<{
    category: string[];
    priceRange: [number, number];
    rating: number;
  }>({
    category: [],
    priceRange: [0, 1000],
    rating: 0
  });

  // Implement API integration for fetching agents
  React.useEffect(() => {
    fetchAgents();
  }, [searchQuery, selectedFilters]);

  const fetchAgents = async () => {
    try {
      // This would use the apiService we created
      // const response = await apiService.getAgents({
      //   search: searchQuery,
      //   category: selectedFilters.category.join(','),
      //   minPrice: selectedFilters.priceRange[0],
      //   maxPrice: selectedFilters.priceRange[1],
      //   minRating: selectedFilters.rating
      // });
      // setAgents(response.agents || []);

      // For now, using mock data
      setAgents([
        {
          id: '1',
          name: 'Customer Support Bot',
          description: 'AI-powered customer support assistant with natural language processing',
          price: 29.99,
          rating: 4.5,
          category: 'Customer Service',
          imageUrl: '/api/placeholder/300/200'
        },
        {
          id: '2',
          name: 'Code Assistant',
          description: 'AI coding assistant for developers with code generation and debugging',
          price: 49.99,
          rating: 4.8,
          category: 'Development',
          imageUrl: '/api/placeholder/300/200'
        },
        {
          id: '3',
          name: 'Content Generator',
          description: 'AI content creation tool for blogs, social media, and marketing',
          price: 19.99,
          rating: 4.2,
          category: 'Content Creation',
          imageUrl: '/api/placeholder/300/200'
        }
      ]);
    } catch (error) {
      console.error('Failed to fetch agents:', error);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Search logic is handled in useEffect
  };

  const handleFilterChange = (filters: typeof selectedFilters) => {
    setSelectedFilters(filters);
    // Filter logic is handled in useEffect
  };

  const filteredAgents = React.useMemo(() => {
    return agents.filter(agent => {
      // Search filter
      if (searchQuery && !agent.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !agent.description.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Category filter
      if (selectedFilters.category.length > 0 &&
          !selectedFilters.category.includes(agent.category)) {
        return false;
      }

      // Price range filter
      if (agent.price < selectedFilters.priceRange[0] ||
          agent.price > selectedFilters.priceRange[1]) {
        return false;
      }

      // Rating filter
      if (agent.rating < selectedFilters.rating) {
        return false;
      }

      return true;
    });
  }, [agents, searchQuery, selectedFilters]);

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          AI Agent Marketplace
        </Typography>

        <Box sx={{ mb: 4 }}>
          <MarketplaceSearch onSearch={handleSearch} />
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            <MarketplaceFilters
              filters={selectedFilters}
              onFilterChange={handleFilterChange}
            />
          </Grid>

          <Grid item xs={12} md={9}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Showing {filteredAgents.length} of {agents.length} agents
              </Typography>
            </Box>
            <Grid container spacing={3}>
              {filteredAgents.length > 0 ? (
                filteredAgents.map((agent) => (
                  <Grid item xs={12} sm={6} md={4} key={agent.id}>
                    <AgentCard agent={agent} />
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Box sx={{ textAlign: 'center', py: 8 }}>
                    <Typography variant="h6" color="text.secondary">
                      No agents found matching your criteria
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Try adjusting your search or filters
                    </Typography>
                  </Box>
                </Grid>
              )}
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default Marketplace;