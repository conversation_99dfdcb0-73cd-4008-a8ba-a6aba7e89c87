const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const servicesConfig = require('../../config/services.config');

class FileUploadService {
  constructor() {
    this.uploadDir = servicesConfig.storage?.local?.uploadPath || './uploads';
    this.maxFileSize = servicesConfig.storage?.local?.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    this.allowedDocumentTypes = ['application/pdf', 'text/plain', 'application/json'];
    this.allowedVideoTypes = ['video/mp4', 'video/webm', 'video/ogg'];

    // Initialize AWS S3 if configured
    if (servicesConfig.storage?.provider === 'aws') {
      this.s3 = new AWS.S3({
        accessKeyId: servicesConfig.storage.aws.accessKeyId,
        secretAccessKey: servicesConfig.storage.aws.secretAccessKey,
        region: servicesConfig.storage.aws.region
      });
      this.s3Bucket = servicesConfig.storage.aws.bucket;
    }

    this.initializeUploadDir();
  }

  async initializeUploadDir() {
    try {
      await fs.mkdir(this.uploadDir, { recursive: true });
      await fs.mkdir(path.join(this.uploadDir, 'images'), { recursive: true });
      await fs.mkdir(path.join(this.uploadDir, 'documents'), { recursive: true });
      await fs.mkdir(path.join(this.uploadDir, 'videos'), { recursive: true });
      await fs.mkdir(path.join(this.uploadDir, 'temp'), { recursive: true });
    } catch (error) {
      console.error('Failed to initialize upload directory:', error);
    }
  }

  // Configure multer for different file types
  getMulterConfig(fileType = 'any') {
    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        let uploadPath = this.uploadDir;

        if (this.allowedImageTypes.includes(file.mimetype)) {
          uploadPath = path.join(this.uploadDir, 'images');
        } else if (this.allowedDocumentTypes.includes(file.mimetype)) {
          uploadPath = path.join(this.uploadDir, 'documents');
        } else if (this.allowedVideoTypes.includes(file.mimetype)) {
          uploadPath = path.join(this.uploadDir, 'videos');
        } else {
          uploadPath = path.join(this.uploadDir, 'temp');
        }

        cb(null, uploadPath);
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = crypto.randomBytes(16).toString('hex');
        const ext = path.extname(file.originalname);
        cb(null, `${Date.now()}-${uniqueSuffix}${ext}`);
      }
    });

    const fileFilter = (req, file, cb) => {
      const allowedTypes = [
        ...this.allowedImageTypes,
        ...this.allowedDocumentTypes,
        ...this.allowedVideoTypes
      ];

      if (fileType === 'image' && !this.allowedImageTypes.includes(file.mimetype)) {
        return cb(new Error('Only image files are allowed'), false);
      }

      if (fileType === 'document' && !this.allowedDocumentTypes.includes(file.mimetype)) {
        return cb(new Error('Only document files are allowed'), false);
      }

      if (fileType === 'video' && !this.allowedVideoTypes.includes(file.mimetype)) {
        return cb(new Error('Only video files are allowed'), false);
      }

      if (fileType === 'any' && !allowedTypes.includes(file.mimetype)) {
        return cb(new Error('File type not allowed'), false);
      }

      cb(null, true);
    };

    return multer({
      storage,
      fileFilter,
      limits: {
        fileSize: this.maxFileSize
      }
    });
  }

  // Upload single file
  async uploadFile(file, options = {}) {
    try {
      const {
        userId,
        agentId,
        category = 'general',
        generateThumbnail = false,
        optimizeImage = false
      } = options;

      let filePath = file.path;
      let thumbnailPath = null;

      // Optimize image if requested
      if (optimizeImage && this.allowedImageTypes.includes(file.mimetype)) {
        filePath = await this.optimizeImage(file.path);
      }

      // Generate thumbnail for images
      if (generateThumbnail && this.allowedImageTypes.includes(file.mimetype)) {
        thumbnailPath = await this.generateThumbnail(filePath);
      }

      // Upload to S3 if configured
      let s3Url = null;
      let s3ThumbnailUrl = null;

      if (this.s3) {
        s3Url = await this.uploadToS3(filePath, file.originalname);
        if (thumbnailPath) {
          s3ThumbnailUrl = await this.uploadToS3(thumbnailPath, `thumb_${file.originalname}`);
        }
      }

      // Create file record
      const fileRecord = {
        originalName: file.originalname,
        filename: path.basename(filePath),
        mimetype: file.mimetype,
        size: file.size,
        path: filePath,
        url: s3Url || `/uploads/${path.relative(this.uploadDir, filePath)}`,
        thumbnailPath,
        thumbnailUrl: s3ThumbnailUrl || (thumbnailPath ? `/uploads/${path.relative(this.uploadDir, thumbnailPath)}` : null),
        category,
        userId,
        agentId,
        uploadedAt: new Date(),
        metadata: {
          width: null,
          height: null,
          duration: null
        }
      };

      // Get image dimensions if it's an image
      if (this.allowedImageTypes.includes(file.mimetype)) {
        try {
          const metadata = await sharp(filePath).metadata();
          fileRecord.metadata.width = metadata.width;
          fileRecord.metadata.height = metadata.height;
        } catch (error) {
          console.error('Failed to get image metadata:', error);
        }
      }

      return fileRecord;
    } catch (error) {
      throw new Error(`File upload failed: ${error.message}`);
    }
  }

  // Upload multiple files
  async uploadMultipleFiles(files, options = {}) {
    try {
      const uploadPromises = files.map(file => this.uploadFile(file, options));
      return await Promise.all(uploadPromises);
    } catch (error) {
      throw new Error(`Multiple file upload failed: ${error.message}`);
    }
  }

  // Optimize image
  async optimizeImage(imagePath) {
    try {
      const optimizedPath = imagePath.replace(/\.[^/.]+$/, '_optimized.jpg');

      await sharp(imagePath)
        .jpeg({ quality: 85, progressive: true })
        .resize(1920, 1080, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .toFile(optimizedPath);

      // Remove original file
      await fs.unlink(imagePath);

      return optimizedPath;
    } catch (error) {
      console.error('Image optimization failed:', error);
      return imagePath; // Return original path if optimization fails
    }
  }

  // Generate thumbnail
  async generateThumbnail(imagePath, size = 300) {
    try {
      const thumbnailPath = imagePath.replace(/\.[^/.]+$/, `_thumb_${size}.jpg`);

      await sharp(imagePath)
        .resize(size, size, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({ quality: 80 })
        .toFile(thumbnailPath);

      return thumbnailPath;
    } catch (error) {
      console.error('Thumbnail generation failed:', error);
      return null;
    }
  }

  // Upload to S3
  async uploadToS3(filePath, originalName) {
    try {
      if (!this.s3) {
        throw new Error('S3 not configured');
      }

      const fileContent = await fs.readFile(filePath);
      const key = `uploads/${Date.now()}-${originalName}`;

      const params = {
        Bucket: this.s3Bucket,
        Key: key,
        Body: fileContent,
        ContentType: this.getMimeType(filePath),
        ACL: 'public-read'
      };

      const result = await this.s3.upload(params).promise();
      return result.Location;
    } catch (error) {
      throw new Error(`S3 upload failed: ${error.message}`);
    }
  }

  // Delete file
  async deleteFile(filePath, s3Url = null) {
    try {
      // Delete from local storage
      if (filePath && await this.fileExists(filePath)) {
        await fs.unlink(filePath);
      }

      // Delete from S3 if URL provided
      if (s3Url && this.s3) {
        const key = s3Url.split('/').pop();
        await this.s3.deleteObject({
          Bucket: this.s3Bucket,
          Key: `uploads/${key}`
        }).promise();
      }

      return true;
    } catch (error) {
      console.error('File deletion failed:', error);
      return false;
    }
  }

  // Validate file
  validateFile(file, allowedTypes = null, maxSize = null) {
    const errors = [];

    // Check file type
    if (allowedTypes && !allowedTypes.includes(file.mimetype)) {
      errors.push(`File type ${file.mimetype} is not allowed`);
    }

    // Check file size
    const sizeLimit = maxSize || this.maxFileSize;
    if (file.size > sizeLimit) {
      errors.push(`File size ${file.size} exceeds limit of ${sizeLimit} bytes`);
    }

    // Check for malicious files
    if (this.isSuspiciousFile(file)) {
      errors.push('File appears to be suspicious or potentially malicious');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Check if file is suspicious
  isSuspiciousFile(file) {
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
    const suspiciousNames = ['autorun.inf', 'desktop.ini'];

    const ext = path.extname(file.originalname).toLowerCase();
    const name = file.originalname.toLowerCase();

    return suspiciousExtensions.includes(ext) || suspiciousNames.includes(name);
  }

  // Get MIME type
  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.mp4': 'video/mp4',
      '.webm': 'video/webm',
      '.ogg': 'video/ogg'
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }

  // Check if file exists
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  // Clean up temporary files
  async cleanupTempFiles(olderThanHours = 24) {
    try {
      const tempDir = path.join(this.uploadDir, 'temp');
      const files = await fs.readdir(tempDir);
      const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime.getTime() < cutoffTime) {
          await fs.unlink(filePath);
          console.log(`Cleaned up temp file: ${file}`);
        }
      }
    } catch (error) {
      console.error('Temp file cleanup failed:', error);
    }
  }

  // Get file info
  async getFileInfo(filePath) {
    try {
      const stats = await fs.stat(filePath);
      const ext = path.extname(filePath).toLowerCase();

      const info = {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        extension: ext,
        mimetype: this.getMimeType(filePath)
      };

      // Add image-specific info
      if (this.allowedImageTypes.includes(info.mimetype)) {
        try {
          const metadata = await sharp(filePath).metadata();
          info.width = metadata.width;
          info.height = metadata.height;
          info.format = metadata.format;
        } catch (error) {
          console.error('Failed to get image info:', error);
        }
      }

      return info;
    } catch (error) {
      throw new Error(`Failed to get file info: ${error.message}`);
    }
  }
}

module.exports = new FileUploadService();
