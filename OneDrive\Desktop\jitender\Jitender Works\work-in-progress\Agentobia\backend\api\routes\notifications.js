const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const notificationService = require('../../services/notificationService');
const Notification = require('../../models/Notification');

// Get user notifications
router.get('/', auth, async (req, res) => {
  try {
    const { page, limit, status, type, unreadOnly } = req.query;
    
    const options = {
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 20,
      status,
      type,
      unreadOnly: unreadOnly === 'true'
    };

    const result = await notificationService.getUserNotifications(req.user.id, options);
    res.json(result);
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get unread notification count
router.get('/unread-count', auth, async (req, res) => {
  try {
    const count = await Notification.getUnreadCount(req.user.id);
    res.json({ count });
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Mark notification as read
router.put('/:id/read', auth, async (req, res) => {
  try {
    const notification = await notificationService.markAsRead(req.params.id, req.user.id);
    res.json(notification);
  } catch (error) {
    console.error('Mark as read error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Mark all notifications as read
router.put('/mark-all-read', auth, async (req, res) => {
  try {
    const result = await notificationService.markAllAsRead(req.user.id);
    res.json({ message: 'All notifications marked as read', modifiedCount: result.modifiedCount });
  } catch (error) {
    console.error('Mark all as read error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete notification
router.delete('/:id', auth, async (req, res) => {
  try {
    const result = await Notification.findOneAndDelete({
      _id: req.params.id,
      recipient: req.user.id
    });

    if (!result) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    res.json({ message: 'Notification deleted successfully' });
  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Create notification (admin only)
router.post('/', auth, async (req, res) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const notification = await notificationService.createNotification(req.body);
    res.status(201).json(notification);
  } catch (error) {
    console.error('Create notification error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Send bulk notifications (admin only)
router.post('/bulk', auth, async (req, res) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { notifications } = req.body;
    
    if (!notifications || !Array.isArray(notifications)) {
      return res.status(400).json({ message: 'Notifications array is required' });
    }

    const results = await notificationService.sendBulkNotifications(notifications);
    res.json({ message: 'Bulk notifications processed', results });
  } catch (error) {
    console.error('Bulk notifications error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get notification statistics (admin only)
router.get('/admin/stats', auth, async (req, res) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { startDate, endDate } = req.query;
    
    // Build date filter
    const dateFilter = {};
    if (startDate) dateFilter.$gte = new Date(startDate);
    if (endDate) dateFilter.$lte = new Date(endDate);

    const query = {};
    if (Object.keys(dateFilter).length > 0) {
      query.createdAt = dateFilter;
    }

    const [
      totalNotifications,
      notificationsByType,
      notificationsByStatus,
      notificationsByPriority
    ] = await Promise.all([
      Notification.countDocuments(query),
      Notification.aggregate([
        { $match: query },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      Notification.aggregate([
        { $match: query },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      Notification.aggregate([
        { $match: query },
        { $group: { _id: '$priority', count: { $sum: 1 } } }
      ])
    ]);

    const stats = {
      totalNotifications,
      notificationsByType,
      notificationsByStatus,
      notificationsByPriority
    };

    res.json(stats);
  } catch (error) {
    console.error('Get notification stats error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Clean up old notifications (admin only)
router.post('/admin/cleanup', auth, async (req, res) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { daysOld = 30 } = req.body;
    const result = await notificationService.cleanupOldNotifications(daysOld);
    
    res.json({ 
      message: 'Old notifications cleaned up', 
      deletedCount: result.deletedCount 
    });
  } catch (error) {
    console.error('Cleanup notifications error:', error);
    res.status(500).json({ error: error.message });
  }
});

// WebSocket endpoint for real-time notifications
router.get('/ws', auth, (req, res) => {
  // This would be handled by WebSocket upgrade
  res.status(400).json({ 
    message: 'This endpoint requires WebSocket upgrade',
    instructions: 'Connect to ws://localhost:3000/api/v1/notifications/ws with proper authentication'
  });
});

module.exports = router;
