const Subscription = require('../models/Subscription');
const Agent = require('../models/Agent');
const User = require('../models/User');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const servicesConfig = require('../../config/services.config');

class SubscriptionService {
  constructor() {
    this.stripe = stripe;
  }

  // Create new subscription
  async createSubscription(userId, agentId, planType, paymentMethodId) {
    try {
      // Get agent details
      const agent = await Agent.findById(agentId);
      if (!agent) {
        throw new Error('Agent not found');
      }

      // Get user details
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if user already has active subscription for this agent
      const existingSubscription = await Subscription.findOne({
        user: userId,
        agent: agentId,
        status: 'active'
      });

      if (existingSubscription) {
        throw new Error('User already has an active subscription for this agent');
      }

      // Get pricing based on plan type
      const pricing = this.getPricingForPlan(agent, planType);
      
      // Create Stripe customer if doesn't exist
      let stripeCustomer;
      try {
        const customers = await this.stripe.customers.list({
          email: user.email,
          limit: 1
        });
        
        if (customers.data.length > 0) {
          stripeCustomer = customers.data[0];
        } else {
          stripeCustomer = await this.stripe.customers.create({
            email: user.email,
            name: user.name,
            metadata: {
              userId: userId.toString()
            }
          });
        }
      } catch (error) {
        throw new Error(`Failed to create Stripe customer: ${error.message}`);
      }

      // Attach payment method to customer
      if (paymentMethodId) {
        await this.stripe.paymentMethods.attach(paymentMethodId, {
          customer: stripeCustomer.id
        });
      }

      // Create subscription in Stripe
      let stripeSubscription;
      if (pricing.interval !== 'one-time') {
        // Create recurring subscription
        stripeSubscription = await this.stripe.subscriptions.create({
          customer: stripeCustomer.id,
          items: [{
            price_data: {
              currency: pricing.currency.toLowerCase(),
              product_data: {
                name: `${agent.name} - ${planType} Plan`,
                description: agent.description
              },
              unit_amount: Math.round(pricing.amount * 100), // Convert to cents
              recurring: {
                interval: pricing.interval === 'yearly' ? 'year' : 'month'
              }
            }
          }],
          default_payment_method: paymentMethodId,
          metadata: {
            userId: userId.toString(),
            agentId: agentId.toString(),
            planType
          }
        });
      } else {
        // Create one-time payment
        const paymentIntent = await this.stripe.paymentIntents.create({
          amount: Math.round(pricing.amount * 100),
          currency: pricing.currency.toLowerCase(),
          customer: stripeCustomer.id,
          payment_method: paymentMethodId,
          confirm: true,
          metadata: {
            userId: userId.toString(),
            agentId: agentId.toString(),
            planType
          }
        });
        
        stripeSubscription = { id: paymentIntent.id };
      }

      // Calculate billing cycle dates
      const startDate = new Date();
      const endDate = new Date();
      
      if (pricing.interval === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else if (pricing.interval === 'yearly') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else {
        // One-time purchase - set end date to 100 years from now
        endDate.setFullYear(endDate.getFullYear() + 100);
      }

      // Create subscription in database
      const subscription = new Subscription({
        user: userId,
        agent: agentId,
        plan: planType,
        status: 'active',
        pricing: {
          amount: pricing.amount,
          currency: pricing.currency,
          interval: pricing.interval
        },
        paymentProvider: 'stripe',
        paymentProviderData: {
          subscriptionId: stripeSubscription.id,
          customerId: stripeCustomer.id,
          paymentMethodId
        },
        billingCycle: {
          startDate,
          endDate,
          nextBillingDate: pricing.interval !== 'one-time' ? endDate : null
        },
        usage: this.getUsageLimitsForPlan(planType),
        features: this.getFeaturesForPlan(planType)
      });

      await subscription.save();

      return await Subscription.findById(subscription._id)
        .populate('user', 'name email')
        .populate('agent', 'name category');

    } catch (error) {
      throw new Error(`Failed to create subscription: ${error.message}`);
    }
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId, reason = 'User requested') {
    try {
      const subscription = await Subscription.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      // Cancel in Stripe if it's a recurring subscription
      if (subscription.paymentProviderData.subscriptionId && 
          subscription.pricing.interval !== 'one-time') {
        await this.stripe.subscriptions.cancel(
          subscription.paymentProviderData.subscriptionId
        );
      }

      // Update subscription status
      subscription.status = 'cancelled';
      subscription.cancelledAt = new Date();
      subscription.cancellationReason = reason;

      await subscription.save();

      return subscription;
    } catch (error) {
      throw new Error(`Failed to cancel subscription: ${error.message}`);
    }
  }

  // Update subscription plan
  async updateSubscriptionPlan(subscriptionId, newPlanType) {
    try {
      const subscription = await Subscription.findById(subscriptionId)
        .populate('agent');
      
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const agent = subscription.agent;
      const newPricing = this.getPricingForPlan(agent, newPlanType);

      // Update in Stripe if it's a recurring subscription
      if (subscription.paymentProviderData.subscriptionId && 
          subscription.pricing.interval !== 'one-time') {
        
        const stripeSubscription = await this.stripe.subscriptions.retrieve(
          subscription.paymentProviderData.subscriptionId
        );

        await this.stripe.subscriptions.update(
          subscription.paymentProviderData.subscriptionId,
          {
            items: [{
              id: stripeSubscription.items.data[0].id,
              price_data: {
                currency: newPricing.currency.toLowerCase(),
                product_data: {
                  name: `${agent.name} - ${newPlanType} Plan`,
                  description: agent.description
                },
                unit_amount: Math.round(newPricing.amount * 100),
                recurring: {
                  interval: newPricing.interval === 'yearly' ? 'year' : 'month'
                }
              }
            }],
            proration_behavior: 'create_prorations'
          }
        );
      }

      // Update subscription in database
      subscription.plan = newPlanType;
      subscription.pricing = {
        amount: newPricing.amount,
        currency: newPricing.currency,
        interval: newPricing.interval
      };
      subscription.usage = this.getUsageLimitsForPlan(newPlanType);
      subscription.features = this.getFeaturesForPlan(newPlanType);

      await subscription.save();

      return subscription;
    } catch (error) {
      throw new Error(`Failed to update subscription: ${error.message}`);
    }
  }

  // Get user subscriptions
  async getUserSubscriptions(userId, status = null) {
    try {
      const query = { user: userId };
      if (status) {
        query.status = status;
      }

      return await Subscription.find(query)
        .populate('agent', 'name category image')
        .sort({ createdAt: -1 });
    } catch (error) {
      throw new Error(`Failed to get user subscriptions: ${error.message}`);
    }
  }

  // Check subscription access
  async checkSubscriptionAccess(userId, agentId) {
    try {
      const subscription = await Subscription.findOne({
        user: userId,
        agent: agentId,
        status: 'active',
        'billingCycle.endDate': { $gt: new Date() }
      });

      return {
        hasAccess: !!subscription,
        subscription: subscription || null,
        isExpired: subscription ? subscription.billingCycle.endDate < new Date() : false
      };
    } catch (error) {
      throw new Error(`Failed to check subscription access: ${error.message}`);
    }
  }

  // Track API usage
  async trackApiUsage(userId, agentId, calls = 1) {
    try {
      const subscription = await Subscription.findOne({
        user: userId,
        agent: agentId,
        status: 'active'
      });

      if (!subscription) {
        throw new Error('No active subscription found');
      }

      await subscription.incrementUsage('apiCalls', calls);

      return {
        usage: subscription.usage.apiCalls,
        limit: subscription.usage.apiLimit,
        remaining: subscription.usage.apiLimit - subscription.usage.apiCalls,
        isLimitExceeded: subscription.isUsageLimitExceeded('apiCalls')
      };
    } catch (error) {
      throw new Error(`Failed to track API usage: ${error.message}`);
    }
  }

  // Helper methods
  getPricingForPlan(agent, planType) {
    const basePricing = {
      free: { amount: 0, currency: 'USD', interval: 'monthly' },
      basic: { amount: 9.99, currency: 'USD', interval: 'monthly' },
      premium: { amount: 29.99, currency: 'USD', interval: 'monthly' },
      enterprise: { amount: 99.99, currency: 'USD', interval: 'monthly' }
    };

    // Use agent's custom pricing if available
    if (agent.pricing && agent.pricing.basePrice) {
      return {
        amount: agent.pricing.basePrice,
        currency: 'USD',
        interval: 'monthly'
      };
    }

    return basePricing[planType] || basePricing.free;
  }

  getUsageLimitsForPlan(planType) {
    const limits = {
      free: { apiCalls: 100, apiLimit: 100, storageUsed: 0, storageLimit: 100 },
      basic: { apiCalls: 0, apiLimit: 1000, storageUsed: 0, storageLimit: 1024 },
      premium: { apiCalls: 0, apiLimit: 10000, storageUsed: 0, storageLimit: 5120 },
      enterprise: { apiCalls: 0, apiLimit: 100000, storageUsed: 0, storageLimit: 51200 }
    };

    return limits[planType] || limits.free;
  }

  getFeaturesForPlan(planType) {
    const features = {
      free: [
        { name: 'Basic Support', enabled: true },
        { name: 'API Access', enabled: true, limit: 100 }
      ],
      basic: [
        { name: 'Email Support', enabled: true },
        { name: 'API Access', enabled: true, limit: 1000 },
        { name: 'Basic Analytics', enabled: true }
      ],
      premium: [
        { name: 'Priority Support', enabled: true },
        { name: 'API Access', enabled: true, limit: 10000 },
        { name: 'Advanced Analytics', enabled: true },
        { name: 'Custom Integrations', enabled: true }
      ],
      enterprise: [
        { name: '24/7 Support', enabled: true },
        { name: 'Unlimited API Access', enabled: true },
        { name: 'Advanced Analytics', enabled: true },
        { name: 'Custom Integrations', enabled: true },
        { name: 'SLA Guarantee', enabled: true },
        { name: 'Custom Development', enabled: true }
      ]
    };

    return features[planType] || features.free;
  }

  // Process subscription renewals
  async processRenewals() {
    try {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      const subscriptionsToRenew = await Subscription.find({
        status: 'active',
        'billingCycle.nextBillingDate': {
          $gte: new Date(),
          $lt: tomorrow
        }
      });

      const results = [];

      for (const subscription of subscriptionsToRenew) {
        try {
          // Reset usage for new billing cycle
          await subscription.resetUsage();

          // Update next billing date
          const nextBillingDate = new Date(subscription.billingCycle.nextBillingDate);
          if (subscription.pricing.interval === 'monthly') {
            nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
          } else if (subscription.pricing.interval === 'yearly') {
            nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
          }

          subscription.billingCycle.nextBillingDate = nextBillingDate;
          subscription.billingCycle.endDate = nextBillingDate;

          await subscription.save();

          results.push({
            subscriptionId: subscription._id,
            status: 'renewed',
            nextBillingDate
          });

        } catch (error) {
          results.push({
            subscriptionId: subscription._id,
            status: 'failed',
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      throw new Error(`Failed to process renewals: ${error.message}`);
    }
  }
}

module.exports = new SubscriptionService();
