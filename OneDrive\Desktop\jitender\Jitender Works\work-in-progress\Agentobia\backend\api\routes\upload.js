const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const fileUploadService = require('../../services/fileUploadService');

// Upload single file
router.post('/single', auth, (req, res) => {
  const upload = fileUploadService.getMulterConfig('any').single('file');
  
  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ error: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    try {
      // Validate file
      const validation = fileUploadService.validateFile(req.file);
      if (!validation.isValid) {
        // Clean up uploaded file
        await fileUploadService.deleteFile(req.file.path);
        return res.status(400).json({ errors: validation.errors });
      }

      // Process file upload
      const fileRecord = await fileUploadService.uploadFile(req.file, {
        userId: req.user.id,
        agentId: req.body.agentId,
        category: req.body.category || 'general',
        generateThumbnail: req.body.generateThumbnail === 'true',
        optimizeImage: req.body.optimizeImage === 'true'
      });

      res.status(201).json({
        message: 'File uploaded successfully',
        file: fileRecord
      });
    } catch (error) {
      console.error('File upload error:', error);
      // Clean up uploaded file on error
      if (req.file && req.file.path) {
        await fileUploadService.deleteFile(req.file.path);
      }
      res.status(500).json({ error: error.message });
    }
  });
});

// Upload multiple files
router.post('/multiple', auth, (req, res) => {
  const upload = fileUploadService.getMulterConfig('any').array('files', 10);
  
  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ error: err.message });
    }

    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    try {
      // Validate all files
      const validationErrors = [];
      const validFiles = [];

      for (const file of req.files) {
        const validation = fileUploadService.validateFile(file);
        if (validation.isValid) {
          validFiles.push(file);
        } else {
          validationErrors.push({
            filename: file.originalname,
            errors: validation.errors
          });
          // Clean up invalid file
          await fileUploadService.deleteFile(file.path);
        }
      }

      if (validFiles.length === 0) {
        return res.status(400).json({ 
          error: 'No valid files to upload',
          validationErrors 
        });
      }

      // Process valid files
      const fileRecords = await fileUploadService.uploadMultipleFiles(validFiles, {
        userId: req.user.id,
        agentId: req.body.agentId,
        category: req.body.category || 'general',
        generateThumbnail: req.body.generateThumbnail === 'true',
        optimizeImage: req.body.optimizeImage === 'true'
      });

      res.status(201).json({
        message: `${fileRecords.length} files uploaded successfully`,
        files: fileRecords,
        validationErrors: validationErrors.length > 0 ? validationErrors : undefined
      });
    } catch (error) {
      console.error('Multiple file upload error:', error);
      // Clean up uploaded files on error
      if (req.files) {
        for (const file of req.files) {
          await fileUploadService.deleteFile(file.path);
        }
      }
      res.status(500).json({ error: error.message });
    }
  });
});

// Upload image with specific validation
router.post('/image', auth, (req, res) => {
  const upload = fileUploadService.getMulterConfig('image').single('image');
  
  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ error: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ error: 'No image uploaded' });
    }

    try {
      const fileRecord = await fileUploadService.uploadFile(req.file, {
        userId: req.user.id,
        agentId: req.body.agentId,
        category: 'image',
        generateThumbnail: true,
        optimizeImage: true
      });

      res.status(201).json({
        message: 'Image uploaded successfully',
        file: fileRecord
      });
    } catch (error) {
      console.error('Image upload error:', error);
      if (req.file && req.file.path) {
        await fileUploadService.deleteFile(req.file.path);
      }
      res.status(500).json({ error: error.message });
    }
  });
});

// Upload document
router.post('/document', auth, (req, res) => {
  const upload = fileUploadService.getMulterConfig('document').single('document');
  
  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ error: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ error: 'No document uploaded' });
    }

    try {
      const fileRecord = await fileUploadService.uploadFile(req.file, {
        userId: req.user.id,
        agentId: req.body.agentId,
        category: 'document'
      });

      res.status(201).json({
        message: 'Document uploaded successfully',
        file: fileRecord
      });
    } catch (error) {
      console.error('Document upload error:', error);
      if (req.file && req.file.path) {
        await fileUploadService.deleteFile(req.file.path);
      }
      res.status(500).json({ error: error.message });
    }
  });
});

// Get file info
router.get('/info/:filename', auth, async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(fileUploadService.uploadDir, filename);
    
    if (!await fileUploadService.fileExists(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    const fileInfo = await fileUploadService.getFileInfo(filePath);
    res.json(fileInfo);
  } catch (error) {
    console.error('Get file info error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete file
router.delete('/:filename', auth, async (req, res) => {
  try {
    const filename = req.params.filename;
    const { s3Url } = req.body;
    
    // In a real application, you would check if the user owns this file
    // or has permission to delete it
    
    const filePath = path.join(fileUploadService.uploadDir, filename);
    const deleted = await fileUploadService.deleteFile(filePath, s3Url);
    
    if (deleted) {
      res.json({ message: 'File deleted successfully' });
    } else {
      res.status(404).json({ error: 'File not found or could not be deleted' });
    }
  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Clean up temporary files (admin only)
router.post('/cleanup', auth, async (req, res) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { olderThanHours = 24 } = req.body;
    await fileUploadService.cleanupTempFiles(olderThanHours);
    
    res.json({ message: 'Temporary files cleaned up successfully' });
  } catch (error) {
    console.error('Cleanup error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get upload statistics (admin only)
router.get('/stats', auth, async (req, res) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const fs = require('fs').promises;
    const path = require('path');
    
    const uploadDir = fileUploadService.uploadDir;
    const stats = {
      totalFiles: 0,
      totalSize: 0,
      filesByType: {},
      directories: {}
    };

    // Get stats for each directory
    const directories = ['images', 'documents', 'videos', 'temp'];
    
    for (const dir of directories) {
      const dirPath = path.join(uploadDir, dir);
      try {
        const files = await fs.readdir(dirPath);
        let dirSize = 0;
        let dirCount = 0;

        for (const file of files) {
          const filePath = path.join(dirPath, file);
          const fileStat = await fs.stat(filePath);
          
          if (fileStat.isFile()) {
            dirSize += fileStat.size;
            dirCount++;
            
            const ext = path.extname(file).toLowerCase();
            stats.filesByType[ext] = (stats.filesByType[ext] || 0) + 1;
          }
        }

        stats.directories[dir] = {
          count: dirCount,
          size: dirSize,
          sizeFormatted: this.formatBytes(dirSize)
        };

        stats.totalFiles += dirCount;
        stats.totalSize += dirSize;
      } catch (error) {
        stats.directories[dir] = { count: 0, size: 0, error: error.message };
      }
    }

    stats.totalSizeFormatted = this.formatBytes(stats.totalSize);

    res.json(stats);
  } catch (error) {
    console.error('Get upload stats error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Helper function to format bytes
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

module.exports = router;
