const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const subscriptionService = require('../../services/subscriptionService');
const Subscription = require('../../models/Subscription');

// Create new subscription
router.post('/', auth, async (req, res) => {
  try {
    const { agentId, planType, paymentMethodId } = req.body;

    if (!agentId || !planType) {
      return res.status(400).json({
        message: 'Agent ID and plan type are required'
      });
    }

    const subscription = await subscriptionService.createSubscription(
      req.user.id,
      agentId,
      planType,
      paymentMethodId
    );

    res.status(201).json(subscription);
  } catch (error) {
    console.error('Create subscription error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Get user's subscriptions
router.get('/my-subscriptions', auth, async (req, res) => {
  try {
    const { status } = req.query;
    const subscriptions = await subscriptionService.getUserSubscriptions(
      req.user.id,
      status
    );

    res.json(subscriptions);
  } catch (error) {
    console.error('Get subscriptions error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get specific subscription
router.get('/:id', auth, async (req, res) => {
  try {
    const subscription = await Subscription.findById(req.params.id)
      .populate('user', 'name email')
      .populate('agent', 'name category image description');

    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    // Check if user owns this subscription or is admin
    if (subscription.user._id.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(subscription);
  } catch (error) {
    console.error('Get subscription error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update subscription plan
router.put('/:id/plan', auth, async (req, res) => {
  try {
    const { planType } = req.body;

    if (!planType) {
      return res.status(400).json({ message: 'Plan type is required' });
    }

    // Check if user owns this subscription
    const subscription = await Subscription.findById(req.params.id);
    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    if (subscription.user.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const updatedSubscription = await subscriptionService.updateSubscriptionPlan(
      req.params.id,
      planType
    );

    res.json(updatedSubscription);
  } catch (error) {
    console.error('Update subscription error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Cancel subscription
router.delete('/:id', auth, async (req, res) => {
  try {
    const { reason } = req.body;

    // Check if user owns this subscription
    const subscription = await Subscription.findById(req.params.id);
    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    if (subscription.user.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const cancelledSubscription = await subscriptionService.cancelSubscription(
      req.params.id,
      reason
    );

    res.json(cancelledSubscription);
  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Check subscription access for an agent
router.get('/access/:agentId', auth, async (req, res) => {
  try {
    const access = await subscriptionService.checkSubscriptionAccess(
      req.user.id,
      req.params.agentId
    );

    res.json(access);
  } catch (error) {
    console.error('Check access error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Track API usage
router.post('/usage/:agentId', auth, async (req, res) => {
  try {
    const { calls = 1 } = req.body;

    const usage = await subscriptionService.trackApiUsage(
      req.user.id,
      req.params.agentId,
      calls
    );

    res.json(usage);
  } catch (error) {
    console.error('Track usage error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Get usage statistics
router.get('/:id/usage', auth, async (req, res) => {
  try {
    const subscription = await Subscription.findById(req.params.id);
    
    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    // Check if user owns this subscription
    if (subscription.user.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const usage = {
      apiCalls: {
        used: subscription.usage.apiCalls,
        limit: subscription.usage.apiLimit,
        remaining: subscription.usage.apiLimit - subscription.usage.apiCalls,
        percentage: (subscription.usage.apiCalls / subscription.usage.apiLimit) * 100
      },
      storage: {
        used: subscription.usage.storageUsed,
        limit: subscription.usage.storageLimit,
        remaining: subscription.usage.storageLimit - subscription.usage.storageUsed,
        percentage: (subscription.usage.storageUsed / subscription.usage.storageLimit) * 100
      },
      billingCycle: subscription.billingCycle,
      features: subscription.features
    };

    res.json(usage);
  } catch (error) {
    console.error('Get usage error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get subscription analytics (for agent owners)
router.get('/analytics/:agentId', auth, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // Check if user owns the agent
    const Agent = require('../../models/Agent');
    const agent = await Agent.findById(req.params.agentId);
    
    if (!agent) {
      return res.status(404).json({ message: 'Agent not found' });
    }

    if (agent.creator.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Build date filter
    const dateFilter = {};
    if (startDate) dateFilter.$gte = new Date(startDate);
    if (endDate) dateFilter.$lte = new Date(endDate);

    const query = { agent: req.params.agentId };
    if (Object.keys(dateFilter).length > 0) {
      query.createdAt = dateFilter;
    }

    // Get subscription analytics
    const [
      totalSubscriptions,
      activeSubscriptions,
      cancelledSubscriptions,
      revenue,
      subscriptionsByPlan
    ] = await Promise.all([
      Subscription.countDocuments(query),
      Subscription.countDocuments({ ...query, status: 'active' }),
      Subscription.countDocuments({ ...query, status: 'cancelled' }),
      Subscription.aggregate([
        { $match: query },
        { $group: { _id: null, total: { $sum: '$pricing.amount' } } }
      ]),
      Subscription.aggregate([
        { $match: query },
        { $group: { _id: '$plan', count: { $sum: 1 }, revenue: { $sum: '$pricing.amount' } } }
      ])
    ]);

    const analytics = {
      overview: {
        totalSubscriptions,
        activeSubscriptions,
        cancelledSubscriptions,
        totalRevenue: revenue[0]?.total || 0,
        conversionRate: totalSubscriptions > 0 ? (activeSubscriptions / totalSubscriptions) * 100 : 0
      },
      subscriptionsByPlan,
      trends: {
        // This would include time-series data for charts
        // Implementation depends on specific analytics requirements
      }
    };

    res.json(analytics);
  } catch (error) {
    console.error('Get analytics error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin routes for subscription management
router.get('/admin/all', auth, async (req, res) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { page = 1, limit = 20, status, plan } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const filter = {};
    if (status) filter.status = status;
    if (plan) filter.plan = plan;

    const subscriptions = await Subscription.find(filter)
      .populate('user', 'name email')
      .populate('agent', 'name category')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));

    const total = await Subscription.countDocuments(filter);

    res.json({
      subscriptions,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error('Admin get subscriptions error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Process subscription renewals (admin/cron job)
router.post('/admin/process-renewals', auth, async (req, res) => {
  try {
    // Check admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const results = await subscriptionService.processRenewals();
    res.json({ message: 'Renewals processed', results });
  } catch (error) {
    console.error('Process renewals error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
