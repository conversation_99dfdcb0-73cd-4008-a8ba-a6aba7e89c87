import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.handleUnauthorized();
        }
        return Promise.reject(error);
      }
    );
  }

  private getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('authToken');
    }
    return null;
  }

  private handleUnauthorized(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
  }

  // Authentication APIs
  async login(email: string, password: string): Promise<any> {
    const response = await this.api.post('/auth/login', { email, password });
    if (response.data.token) {
      localStorage.setItem('authToken', response.data.token);
    }
    return response.data;
  }

  async register(userData: any): Promise<any> {
    const response = await this.api.post('/auth/register', userData);
    if (response.data.token) {
      localStorage.setItem('authToken', response.data.token);
    }
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } finally {
      localStorage.removeItem('authToken');
    }
  }

  async getCurrentUser(): Promise<any> {
    const response = await this.api.get('/auth/me');
    return response.data;
  }

  // Agent APIs
  async getAgents(params?: any): Promise<any> {
    const response = await this.api.get('/agents', { params });
    return response.data;
  }

  async getAgent(id: string): Promise<any> {
    const response = await this.api.get(`/agents/${id}`);
    return response.data;
  }

  async createAgent(agentData: any): Promise<any> {
    const response = await this.api.post('/agents', agentData);
    return response.data;
  }

  async updateAgent(id: string, agentData: any): Promise<any> {
    const response = await this.api.put(`/agents/${id}`, agentData);
    return response.data;
  }

  async deleteAgent(id: string): Promise<any> {
    const response = await this.api.delete(`/agents/${id}`);
    return response.data;
  }

  // Agent Reviews APIs
  async getAgentReviews(agentId: string, params?: any): Promise<any> {
    const response = await this.api.get(`/agents/${agentId}/reviews`, { params });
    return response.data;
  }

  async createReview(agentId: string, reviewData: any): Promise<any> {
    const response = await this.api.post(`/agents/${agentId}/reviews`, reviewData);
    return response.data;
  }

  async updateReview(reviewId: string, reviewData: any): Promise<any> {
    const response = await this.api.put(`/agents/reviews/${reviewId}`, reviewData);
    return response.data;
  }

  async deleteReview(reviewId: string): Promise<any> {
    const response = await this.api.delete(`/agents/reviews/${reviewId}`);
    return response.data;
  }

  // Subscription APIs
  async getMySubscriptions(status?: string): Promise<any> {
    const params = status ? { status } : {};
    const response = await this.api.get('/subscriptions/my-subscriptions', { params });
    return response.data;
  }

  async createSubscription(subscriptionData: any): Promise<any> {
    const response = await this.api.post('/subscriptions', subscriptionData);
    return response.data;
  }

  async updateSubscriptionPlan(subscriptionId: string, planType: string): Promise<any> {
    const response = await this.api.put(`/subscriptions/${subscriptionId}/plan`, { planType });
    return response.data;
  }

  async cancelSubscription(subscriptionId: string, reason?: string): Promise<any> {
    const response = await this.api.delete(`/subscriptions/${subscriptionId}`, { data: { reason } });
    return response.data;
  }

  async checkSubscriptionAccess(agentId: string): Promise<any> {
    const response = await this.api.get(`/subscriptions/access/${agentId}`);
    return response.data;
  }

  async getSubscriptionUsage(subscriptionId: string): Promise<any> {
    const response = await this.api.get(`/subscriptions/${subscriptionId}/usage`);
    return response.data;
  }

  // Payment APIs
  async createPaymentIntent(paymentData: any): Promise<any> {
    const response = await this.api.post('/payments/create-intent', paymentData);
    return response.data;
  }

  async confirmPayment(paymentIntentId: string): Promise<any> {
    const response = await this.api.post('/payments/confirm', { paymentIntentId });
    return response.data;
  }

  async getPaymentHistory(params?: any): Promise<any> {
    const response = await this.api.get('/payments/history', { params });
    return response.data;
  }

  // File Upload APIs
  async uploadFile(file: File, options?: any): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (options) {
      Object.keys(options).forEach(key => {
        formData.append(key, options[key]);
      });
    }

    const response = await this.api.post('/upload/single', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async uploadMultipleFiles(files: File[], options?: any): Promise<any> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });
    
    if (options) {
      Object.keys(options).forEach(key => {
        formData.append(key, options[key]);
      });
    }

    const response = await this.api.post('/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async uploadImage(image: File, options?: any): Promise<any> {
    const formData = new FormData();
    formData.append('image', image);
    
    if (options) {
      Object.keys(options).forEach(key => {
        formData.append(key, options[key]);
      });
    }

    const response = await this.api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Notification APIs
  async getNotifications(params?: any): Promise<any> {
    const response = await this.api.get('/notifications', { params });
    return response.data;
  }

  async getUnreadNotificationCount(): Promise<any> {
    const response = await this.api.get('/notifications/unread-count');
    return response.data;
  }

  async markNotificationAsRead(notificationId: string): Promise<any> {
    const response = await this.api.put(`/notifications/${notificationId}/read`);
    return response.data;
  }

  async markAllNotificationsAsRead(): Promise<any> {
    const response = await this.api.put('/notifications/mark-all-read');
    return response.data;
  }

  async deleteNotification(notificationId: string): Promise<any> {
    const response = await this.api.delete(`/notifications/${notificationId}`);
    return response.data;
  }

  // Admin APIs
  async getDashboardStats(): Promise<any> {
    const response = await this.api.get('/admin/dashboard/stats');
    return response.data;
  }

  async getAdminUsers(params?: any): Promise<any> {
    const response = await this.api.get('/admin/users', { params });
    return response.data;
  }

  async updateUserStatus(userId: string, isActive: boolean): Promise<any> {
    const response = await this.api.put(`/admin/users/${userId}/status`, { isActive });
    return response.data;
  }

  async getAdminAgents(params?: any): Promise<any> {
    const response = await this.api.get('/admin/agents', { params });
    return response.data;
  }

  async approveAgent(agentId: string): Promise<any> {
    const response = await this.api.put(`/admin/agents/${agentId}/approve`);
    return response.data;
  }

  async rejectAgent(agentId: string, reason: string): Promise<any> {
    const response = await this.api.put(`/admin/agents/${agentId}/reject`, { reason });
    return response.data;
  }

  async getRevenueStats(period?: string): Promise<any> {
    const params = period ? { period } : {};
    const response = await this.api.get('/admin/revenue/stats', { params });
    return response.data;
  }

  async getAnalytics(period?: string): Promise<any> {
    const params = period ? { period } : {};
    const response = await this.api.get('/admin/analytics', { params });
    return response.data;
  }

  // Generic API method
  async request(method: string, url: string, data?: any, config?: any): Promise<any> {
    const response = await this.api.request({
      method,
      url,
      data,
      ...config,
    });
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
