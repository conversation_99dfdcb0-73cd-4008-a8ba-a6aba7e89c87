const mongoose = require('mongoose');

const subscriptionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  agent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Agent',
    required: true
  },
  plan: {
    type: String,
    enum: ['free', 'basic', 'premium', 'enterprise'],
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'cancelled', 'expired', 'pending'],
    default: 'pending'
  },
  pricing: {
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      default: 'USD'
    },
    interval: {
      type: String,
      enum: ['monthly', 'yearly', 'one-time'],
      default: 'monthly'
    }
  },
  paymentProvider: {
    type: String,
    enum: ['stripe', 'cashfree', 'paypal'],
    required: true
  },
  paymentProviderData: {
    subscriptionId: String,
    customerId: String,
    paymentMethodId: String
  },
  billingCycle: {
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    nextBillingDate: Date,
    trialEndDate: Date
  },
  usage: {
    apiCalls: {
      type: Number,
      default: 0
    },
    apiLimit: {
      type: Number,
      default: 1000
    },
    storageUsed: {
      type: Number,
      default: 0
    },
    storageLimit: {
      type: Number,
      default: 1024 // MB
    }
  },
  features: [{
    name: String,
    enabled: {
      type: Boolean,
      default: true
    },
    limit: Number
  }],
  discounts: [{
    code: String,
    type: {
      type: String,
      enum: ['percentage', 'fixed']
    },
    value: Number,
    appliedAt: {
      type: Date,
      default: Date.now
    }
  }],
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  cancelledAt: Date,
  cancellationReason: String
});

// Indexes for better query performance
subscriptionSchema.index({ user: 1, agent: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ 'billingCycle.endDate': 1 });
subscriptionSchema.index({ 'billingCycle.nextBillingDate': 1 });

// Update the updatedAt timestamp before saving
subscriptionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Virtual for checking if subscription is active
subscriptionSchema.virtual('isActive').get(function() {
  return this.status === 'active' && 
         this.billingCycle.endDate > new Date();
});

// Virtual for checking if subscription is in trial
subscriptionSchema.virtual('isInTrial').get(function() {
  return this.billingCycle.trialEndDate && 
         this.billingCycle.trialEndDate > new Date();
});

// Method to check if usage limit is exceeded
subscriptionSchema.methods.isUsageLimitExceeded = function(type = 'apiCalls') {
  if (type === 'apiCalls') {
    return this.usage.apiCalls >= this.usage.apiLimit;
  }
  if (type === 'storage') {
    return this.usage.storageUsed >= this.usage.storageLimit;
  }
  return false;
};

// Method to increment usage
subscriptionSchema.methods.incrementUsage = function(type, amount = 1) {
  if (type === 'apiCalls') {
    this.usage.apiCalls += amount;
  } else if (type === 'storage') {
    this.usage.storageUsed += amount;
  }
  return this.save();
};

// Method to reset usage (for new billing cycle)
subscriptionSchema.methods.resetUsage = function() {
  this.usage.apiCalls = 0;
  this.usage.storageUsed = 0;
  return this.save();
};

// Static method to find active subscriptions
subscriptionSchema.statics.findActiveSubscriptions = function(userId) {
  return this.find({
    user: userId,
    status: 'active',
    'billingCycle.endDate': { $gt: new Date() }
  }).populate('agent', 'name category');
};

// Static method to find expiring subscriptions
subscriptionSchema.statics.findExpiringSubscriptions = function(days = 7) {
  const expirationDate = new Date();
  expirationDate.setDate(expirationDate.getDate() + days);
  
  return this.find({
    status: 'active',
    'billingCycle.endDate': { 
      $gte: new Date(),
      $lte: expirationDate 
    }
  }).populate('user', 'name email')
    .populate('agent', 'name');
};

module.exports = mongoose.model('Subscription', subscriptionSchema);
