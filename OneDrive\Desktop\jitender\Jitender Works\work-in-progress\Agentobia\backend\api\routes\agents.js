const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const User = require('../../models/User');
const Agent = require('../../models/Agent');
const AgentReview = require('../../models/AgentReview');

// Get all agents with filtering and pagination
router.get('/', async (req, res) => {
  try {
    const {
      category,
      subscriptionType,
      minPrice,
      maxPrice,
      minRating,
      search,
      page = 1,
      limit = 12,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = { status: 'active' };

    if (category) filter.category = category;
    if (subscriptionType) filter.subscriptionType = subscriptionType;
    if (minPrice || maxPrice) {
      filter.price = {};
      if (minPrice) filter.price.$gte = Number(minPrice);
      if (maxPrice) filter.price.$lte = Number(maxPrice);
    }
    if (minRating) filter.rating = { $gte: Number(minRating) };
    if (search) {
      filter.$text = { $search: search };
    }

    // Calculate pagination
    const skip = (Number(page) - 1) * Number(limit);

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const agents = await Agent.find(filter)
      .populate('creator', 'name email')
      .sort(sort)
      .skip(skip)
      .limit(Number(limit));

    const total = await Agent.countDocuments(filter);

    res.json({
      agents,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get agent by ID with reviews
router.get('/:id', async (req, res) => {
  try {
    const agent = await Agent.findById(req.params.id)
      .populate('creator', 'name email')
      .populate({
        path: 'reviews',
        populate: {
          path: 'user',
          select: 'name'
        }
      });

    if (!agent) {
      return res.status(404).json({ message: 'Agent not found' });
    }

    // Get reviews for this agent
    const reviews = await AgentReview.find({ agent: req.params.id })
      .populate('user', 'name')
      .sort({ createdAt: -1 })
      .limit(10);

    res.json({
      ...agent.toObject(),
      reviews
    });
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Agent not found' });
    }
    res.status(500).json({ error: 'Server error' });
  }
});

// Create new agent
router.post('/', auth, async (req, res) => {
  try {
    const {
      name,
      description,
      category,
      subscriptionType,
      pricing,
      price,
      image,
      features,
      requirements,
      version,
      deploymentConfig
    } = req.body;

    // Validate required fields
    if (!name || !description || !category || !price) {
      return res.status(400).json({
        message: 'Name, description, category, and price are required'
      });
    }

    const agent = new Agent({
      name,
      description,
      category,
      subscriptionType: subscriptionType || 'free',
      pricing,
      price,
      image,
      creator: req.user.id,
      features: features || [],
      requirements: requirements || {},
      version: version || '1.0.0',
      deploymentConfig: deploymentConfig || {},
      status: 'pending'
    });

    await agent.save();

    const populatedAgent = await Agent.findById(agent._id)
      .populate('creator', 'name email');

    res.status(201).json(populatedAgent);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: 'Server error' });
  }
});

// Update agent profile
router.put('/profile', auth, async (req, res) => {
  try {
    const { phoneNumber, address, company } = req.body;
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.role !== 'agent') {
      return res.status(403).json({ message: 'Not authorized' });
    }

    user.profile = {
      phoneNumber: phoneNumber || user.profile.phoneNumber,
      address: address || user.profile.address,
      company: company || user.profile.company
    };

    await user.save();
    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// Become an agent
router.post('/become-agent', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.role === 'agent') {
      return res.status(400).json({ message: 'User is already an agent' });
    }

    user.role = 'agent';
    await user.save();

    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// Add review to agent
router.post('/:id/reviews', auth, async (req, res) => {
  try {
    const { rating, comment } = req.body;
    const agentId = req.params.id;

    // Validate rating
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        message: 'Rating must be between 1 and 5'
      });
    }

    // Check if agent exists
    const agent = await Agent.findById(agentId);
    if (!agent) {
      return res.status(404).json({ message: 'Agent not found' });
    }

    // Check if user already reviewed this agent
    const existingReview = await AgentReview.findOne({
      agent: agentId,
      user: req.user.id
    });

    if (existingReview) {
      return res.status(400).json({
        message: 'You have already reviewed this agent'
      });
    }

    // Create review
    const review = new AgentReview({
      agent: agentId,
      user: req.user.id,
      rating,
      comment: comment || '',
      createdAt: new Date()
    });

    await review.save();

    // Update agent's average rating
    const reviews = await AgentReview.find({ agent: agentId });
    const averageRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

    await Agent.findByIdAndUpdate(agentId, {
      rating: Math.round(averageRating * 10) / 10
    });

    const populatedReview = await AgentReview.findById(review._id)
      .populate('user', 'name')
      .populate('agent', 'name');

    res.status(201).json(populatedReview);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get reviews for an agent
router.get('/:id/reviews', async (req, res) => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const reviews = await AgentReview.find({ agent: req.params.id })
      .populate('user', 'name')
      .sort(sort)
      .skip(skip)
      .limit(Number(limit));

    const total = await AgentReview.countDocuments({ agent: req.params.id });

    res.json({
      reviews,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: 'Server error' });
  }
});

// Update review
router.put('/reviews/:reviewId', auth, async (req, res) => {
  try {
    const { rating, comment } = req.body;
    const reviewId = req.params.reviewId;

    const review = await AgentReview.findById(reviewId);
    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Check if user owns this review
    if (review.user.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to update this review' });
    }

    // Validate rating
    if (rating && (rating < 1 || rating > 5)) {
      return res.status(400).json({
        message: 'Rating must be between 1 and 5'
      });
    }

    // Update review
    if (rating) review.rating = rating;
    if (comment !== undefined) review.comment = comment;
    review.updatedAt = new Date();

    await review.save();

    // Recalculate agent's average rating
    const reviews = await AgentReview.find({ agent: review.agent });
    const averageRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

    await Agent.findByIdAndUpdate(review.agent, {
      rating: Math.round(averageRating * 10) / 10
    });

    const populatedReview = await AgentReview.findById(review._id)
      .populate('user', 'name')
      .populate('agent', 'name');

    res.json(populatedReview);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: 'Server error' });
  }
});

// Delete review
router.delete('/reviews/:reviewId', auth, async (req, res) => {
  try {
    const reviewId = req.params.reviewId;

    const review = await AgentReview.findById(reviewId);
    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Check if user owns this review or is admin
    const user = await User.findById(req.user.id);
    if (review.user.toString() !== req.user.id && user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to delete this review' });
    }

    const agentId = review.agent;
    await AgentReview.findByIdAndDelete(reviewId);

    // Recalculate agent's average rating
    const reviews = await AgentReview.find({ agent: agentId });
    const averageRating = reviews.length > 0
      ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
      : 0;

    await Agent.findByIdAndUpdate(agentId, {
      rating: Math.round(averageRating * 10) / 10
    });

    res.json({ message: 'Review deleted successfully' });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;