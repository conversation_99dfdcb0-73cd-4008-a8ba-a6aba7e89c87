const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const WebSocket = require('ws');
const servicesConfig = require('../../config/services.config');

const app = express();
const server = require('http').createServer(app);

// Middleware
app.use(cors());
app.use(express.json());

// Database Connection
mongoose.connect(servicesConfig.database.url, servicesConfig.database.options)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// WebSocket Server for AI Agents
const wss = new WebSocket.Server({ server });
wss.on('connection', (ws) => {
  console.log('New AI Agent WebSocket connection');
  ws.on('message', (message) => {
    // Handle real-time AI agent updates
    console.log('Received:', message);
  });
});

// API Routes
app.use('/api/v1/auth', require('./routes/auth'));
app.use('/api/v1/agents', require('./routes/agents'));
app.use('/api/v1/payments', require('./routes/payments'));
app.use('/api/v1/admin', require('./routes/admin'));
app.use('/api/v1/analytics', require('./routes/analytics.routes'));
app.use('/api/v1/subscriptions', require('./routes/subscriptions'));
app.use('/api/v1/upload', require('./routes/upload'));
app.use('/api/v1/notifications', require('./routes/notifications'));

// Error Handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Backend server running on port ${PORT}`);
});

module.exports = server;